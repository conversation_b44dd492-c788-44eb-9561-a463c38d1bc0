<template>
  <div class="expert-database">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="专家姓名">
          <el-input
            v-model="searchForm.expertName"
            placeholder="请输入专家姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="专业标签">
          <el-input
            v-model="searchForm.professionalTag"
            placeholder="请输入专业标签"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="所在地区">
          <el-input
            v-model="searchForm.location"
            placeholder="请选择地区"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="职称">
          <el-input
            v-model="searchForm.title"
            placeholder="请选择职称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 专家卡片展示区域 -->
    <div class="expert-cards-container">
      <div class="expert-grid">
        <div
          v-for="expert in expertList"
          :key="expert.id"
          class="expert-card"
          @click="handleExpertClick(expert)"
        >
          <div
            class="expert-avatar"
            style="display: flex; justify-content: center; align-items: center"
          >
            <img :src="expert.avatar || defaultAvatar" :alt="expert.name" />
          </div>
          <div class="expert-info">
            <div class="expert-name"
              >{{ expert.name }} <span class="expert-work">{{ expert.work }}</span>
            </div>
            <div class="expert-tags">
              <span v-for="tag in expert.tags" :key="tag" class="expert-tag">
                {{ tag }}
              </span>
            </div>
            <div class="expert-phone"> 电话：{{ expert.phone }} </div>
            <div class="expert-bottom">
              <div class="expert-location">
                {{ expert.location }}
              </div>
              <div class="expert-actions">
                <div class="action-btn" @click.stop="handleContact(expert)">
                  <div class="chat-icon">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                  </div>
                  <div class="action-text">立即咨询</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExpertDatabase',
  data() {
    return {
      // 搜索表单
      searchForm: {
        expertName: '',
        professionalTag: '',
        location: '',
        title: ''
      },
      // 分页信息
      pagination: {
        currentPage: 1,
        pageSize: 12,
        total: 0
      },
      // 专家列表
      expertList: [],
      // 默认头像
      defaultAvatar: '/src/assets/imgs/avatar.jpg',
      // 模拟专家数据
      mockExpertData: [
        {
          id: 1,
          name: '周晚舟',
          tags: ['大豆', '油菜'],
          phone: '18905162873',
          location: '石家庄长安区',
          avatar: '',
          work: '教授',
          department: '农业科学院'
        },
        {
          id: 2,
          name: '林星照',
          tags: ['大豆', '金针菇'],
          phone: '18905162871',
          location: '石家庄裕华区',
          avatar: '',
          work: '教授',
          department: '农业大学'
        },
        {
          id: 3,
          name: '苏硕铃',
          tags: ['红薯', '油菜'],
          phone: '18503167821',
          location: '保定市',
          avatar: '',
          work: '讲师',
          department: '农业技术推广站'
        },
        {
          id: 4,
          name: '李若宁',
          tags: ['玉米'],
          phone: '18905162872',
          location: '唐山路北区',
          avatar: '',
          work: '讲师',
          department: '农业科学院'
        },
        {
          id: 5,
          name: '赵清和',
          tags: ['小麦'],
          phone: '18503167823',
          location: '唐山路南区',
          avatar: '',
          work: '副教授',
          department: '农业大学'
        },
        {
          id: 6,
          name: '周晓舟',
          tags: ['大豆'],
          phone: '18905162873',
          location: '石家庄长安区',
          avatar: '',
          work: '教授',
          department: '农业科学院'
        },
        {
          id: 7,
          name: '王亦辰',
          tags: ['高粱'],
          phone: '18503167823',
          location: '石家庄裕华区',
          avatar: '',
          work: '讲师',
          department: '农业技术推广站'
        },
        {
          id: 8,
          name: '吴疏影',
          tags: ['玉米'],
          phone: '18905162873',
          location: '保定市',
          avatar: '',
          work: '讲师',
          department: '农业大学'
        },
        {
          id: 9,
          name: '郑知夏',
          tags: ['大豆'],
          phone: '18905162874',
          location: '唐山路北区',
          avatar: '',
          work: '教授',
          department: '农业科学院'
        },
        {
          id: 10,
          name: '马明宇',
          tags: ['玉米'],
          phone: '18905162874',
          location: '石家庄裕华区',
          avatar: '',
          work: '讲师',
          department: '农业大学'
        },
        {
          id: 11,
          name: '冯硕知',
          tags: ['玉米'],
          phone: '18905162874',
          location: '石家庄裕华区',
          avatar: '',
          work: '讲师',
          department: '农业技术推广站'
        },
        {
          id: 12,
          name: '吴疏影',
          tags: ['玉米'],
          phone: '18905162874',
          location: '保定市',
          avatar: '',
          work: '讲师',
          department: '农业大学'
        }
      ]
    }
  },
  mounted() {
    this.loadExpertData()
  },
  methods: {
    // 加载专家数据
    loadExpertData() {
      // 模拟API调用
      setTimeout(() => {
        this.expertList = this.mockExpertData
        this.pagination.total = this.mockExpertData.length
      }, 300)
    },

    // 搜索处理
    handleSearch() {
      console.log('搜索条件:', this.searchForm)
      // 这里应该调用API进行搜索
      this.loadExpertData()
      this.$message.success('搜索完成')
    },

    // 重置搜索
    handleReset() {
      Object.keys(this.searchForm).forEach((key) => {
        this.searchForm[key] = ''
      })
      this.loadExpertData()
    },

    // 专家卡片点击
    handleExpertClick(expert) {
      console.log('点击专家:', expert)
      this.$message.info(`查看 ${expert.name} 的详细信息`)
    },

    // 联系专家
    handleContact(expert) {
      console.log('联系专家:', expert)
      this.$message.success(`正在联系 ${expert.name}`)
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.loadExpertData()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadExpertData()
    }
  }
}
</script>

<style lang="scss" scoped>
.expert-database {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .search-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 20px;

        :deep(.el-form-item__label) {
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  .expert-cards-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .expert-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
      margin-bottom: 30px;

      .expert-card {
        background: white;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
          border-color: #4285f4;
        }

        .expert-avatar {
          text-align: center;
          margin-bottom: 15px;

          img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #f0f0f0;
          }
        }

        .expert-info {
          text-align: center;

          .expert-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }

          .expert-work {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            font-weight: 500;
          }

          .expert-tags {
            margin-bottom: 12px;

            .expert-tag {
              display: inline-block;
              background: #e8f4fd;
              color: #4285f4;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 12px;
              margin: 2px 4px;
              border: 1px solid #d1e9ff;
            }
          }

          .expert-phone {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
          }

          .expert-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
          }

          .expert-location {
            color: #e74c3c;
            font-size: 13px;
            background: #f8f9fa;
            padding: 6px 12px;
            border-radius: 15px;
            display: inline-block;
          }

          .expert-actions {
            .action-btn {
              display: flex;
              flex-direction: column;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;

              .chat-icon {
                background: #52c41a;
                width: 50px;
                height: 30px;
                border-radius: 25px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 4px;
                margin-bottom: 6px;
                transition: all 0.3s ease;

                .dot {
                  width: 6px;
                  height: 6px;
                  background: white;
                  border-radius: 50%;
                }
              }

              .action-text {
                color: #333;
                font-size: 12px;
                font-weight: 500;
              }

              &:hover {
                .chat-icon {
                  background: #389e0d;
                  transform: scale(1.05);
                }

                .action-text {
                  color: #52c41a;
                }
              }
            }
          }
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;

      :deep(.el-pagination) {
        .el-pager li {
          &.is-active {
            background-color: #4285f4;
            color: white;
          }
        }

        .btn-next,
        .btn-prev {
          &:hover {
            color: #4285f4;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .expert-database {
    padding: 10px;

    .search-container {
      padding: 15px;

      .search-form {
        .el-form-item {
          margin-right: 0;
          width: 100%;

          :deep(.el-input) {
            width: 100% !important;
          }
        }
      }
    }

    .expert-cards-container {
      padding: 15px;

      .expert-grid {
        grid-template-columns: 1fr;
        gap: 15px;
      }
    }
  }
}
</style>
